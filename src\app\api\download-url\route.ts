import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { videoId, quality, downloadType } = await request.json();
    
    if (!videoId) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      );
    }

    if (!quality || !downloadType) {
      return NextResponse.json(
        { error: 'Quality and download type are required' },
        { status: 400 }
      );
    }

    // Create mock download URL based on type and quality
    let downloadUrl: string;
    let title = `YouTube Video ${videoId} (Mock Data)`;
    
    if (downloadType === 'audio') {
      downloadUrl = `https://mock-download-url.com/audio/${videoId}_${quality}kbps.mp3`;
    } else if (downloadType === 'video') {
      downloadUrl = `https://mock-download-url.com/video/${videoId}_${quality}p.mp4`;
    } else {
      return NextResponse.json(
        { error: 'Invalid download type. Must be "audio" or "video"' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      status: true,
      data: {
        downloadUrl,
        title,
        thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
        duration: "3:45"
      }
    });

  } catch (error) {
    console.error('Error getting download URL:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get download URL',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
